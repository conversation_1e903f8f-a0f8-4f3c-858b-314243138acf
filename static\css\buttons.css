.btn-primary {
   background-color: var(--accent-blue);
   border: none;
   color: #fff;
   font-weight: 600;
   border-radius: 10px;
   /* matches your sidebar/cards */
   padding: 0.65rem 1.25rem;
   transition: all 0.3s ease;
   box-shadow: 0 4px 12px rgba(0, 174, 239, 0.15);
   /* subtle elevation */
}

.btn-primary:hover {
   background-color: var(--accent-blue-light);
   transform: translateY(-2px);
   /* lift on hover */
   box-shadow: 0 8px 25px rgba(0, 174, 239, 0.2);
   /* stronger shadow */
}

.btn-primary:active {
   transform: translateY(0);
   /* press effect */
   box-shadow: 0 4px 12px rgba(0, 174, 239, 0.15);
}

/* Target only flash alert close buttons */
.flash-alert .btn-close {
   background: none;
   position: relative;
   width: 1.5rem;
   height: 1.5rem;
   opacity: 1 !important;
   border: none;
   box-shadow: none !important;
   outline: none !important;
   cursor: pointer;
}

/* Use currentColor so it automatically matches alert text color */
.flash-alert .btn-close::before {
   content: "\00d7";
   /* Unicode × */
   font-family: Arial, Helvetica, sans-serif;
   font-size: 1.5rem;
   font-weight: 300;
   color: currentColor;
   /* <-- this makes X inherit from .alert-* */
   line-height: 1;
   position: absolute;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
   transition: transform 0.2s, color 0.2s;
}

/* Hover effect: slightly darker/lighter */
.flash-alert.alert-success .btn-close::before {
   color: rgba(0, 255, 72, 1);
}

.flash-alert.alert-danger .btn-close::before {
   color: red;
}

.flash-alert.alert-warning .btn-close::before {
   color: orange;
}

.flash-alert.alert-info .btn-close::before {
   color: var(--accent-blue);

}