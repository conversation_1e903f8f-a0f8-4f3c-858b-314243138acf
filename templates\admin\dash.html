{% extends "base.html" %}
{% block title %}Admin Panel | DevSphere.run{% endblock %}

{% block content %}
<div class="container py-5">
    <h2 class="mb-4">User Management</h2>

    <table class="table table-hover align-middle">
        <thead class="table-dark text-info">
            <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody class="text-light">
            {% for user in users %}
            <tr>
                <td>{{ user.username }}</td>
                <td>{{ user.email }}</td>
                <td>{{ user.role_display }}</td>
                <td>
                    {% if user.is_active %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </td>
                <td>
                    {% if user != current_user %}
                    <form method="POST" action="{{ url_for('admin_change_role', user_id=user.id) }}" class="d-inline">
                        <select name="role" class="form-select form-select-sm d-inline w-auto me-2">
                            <option value="user" {% if user.role == 'user' %}selected{% endif %}>User</option>
                            <option value="moderator" {% if user.role == 'moderator' %}selected{% endif %}>Moderator</option>
                            <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin</option>
                        </select>
                        <button type="submit" class="btn btn-sm btn-primary">Update</button>
                    </form>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
