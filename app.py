from flask import Flask, render_template
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user
from models import db, User
from auth import auth_bp
from main import main_bp
from threads import thread_bp
from snippets import snippets_bp
from config import Config
from flask_migrate import Migrate
from flask_ckeditor import CKE<PERSON><PERSON>

def create_app():
    
    app = Flask(__name__)
    app.config.from_object(Config)
    
    ckeditor = CKEditor(app)
    app.config['CKEDITOR_PKG_TYPE'] = 'full'
    
    db.init_app(app)

    migrate = Migrate(app, db)
    
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(user_id)
    
    app.register_blueprint(main_bp) 
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(snippets_bp, url_prefix='/snippets')
    app.register_blueprint(thread_bp, url_prefix='')
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    with app.app_context():
        db.create_all()
    
    app.run(host='0.0.0.0', port=5000, debug=True)