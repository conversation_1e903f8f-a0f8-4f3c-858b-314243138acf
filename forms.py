from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField, BooleanField, TextAreaField, SelectField
from wtforms.validators import <PERSON>Required, Email, Length, EqualTo, ValidationError
from models import User
from flask_ckeditor import CKEditorField

class RegistrationForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()], render_kw={"placeholder": "Enter your username"})
    password = PasswordField('Password', validators=[
        DataRequired(), 
        Length(min=8, message='Password must be at least 8 characters long')
    ], render_kw={"placeholder": "Create a password"})
    confirm_password = PasswordField('Confirm Password', 
                                    validators=[DataRequired(), EqualTo('password')],
                                    render_kw={"placeholder": "Confirm your password"})
    submit = SubmitField('Sign Up')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('Email already registered. Please choose a different one.')

class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()],
                       render_kw={"placeholder": "Enter your username"})
    password = PasswordField('Password', validators=[DataRequired()],
                            render_kw={"placeholder": "Enter your password"})
    remember = BooleanField('Remember Me')
    submit = SubmitField('Login')
    
class SnippetForm(FlaskForm):
    title = StringField("Title", validators=[DataRequired(), Length(max=200)])
    description = CKEditorField("Description")
    code = TextAreaField("Code")
    language = SelectField(
        "Language",
        choices=[("python", "Python"), ("javascript", "JavaScript"),
                 ("java", "Java"), ("c++", "C++"), ("go", "Go"),
                 ("php", "PHP"), ("ruby", "Ruby"), ("csharp", "C#")],
        validators=[DataRequired()]
    )
    tags = StringField("Tags (comma separated)")
    submit = SubmitField("Save Snippet")
    
class ThreadForm(FlaskForm):
    title = StringField('Title', validators=[DataRequired()])
    body = TextAreaField('Content', validators=[DataRequired()])
    submit = SubmitField('Post')

class CommentForm(FlaskForm):
    body = TextAreaField('Comment', validators=[DataRequired()])
    submit = SubmitField('Reply')