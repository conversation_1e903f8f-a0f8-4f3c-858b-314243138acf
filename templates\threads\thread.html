{% extends "base.html" %}
{% block content %}
<div class="container my-5">

  <!-- Thread Card -->
  <div class="card shadow-sm rounded-3 mb-4">
    <div class="card-body">
      <h2 class="card-title fw-bold">{{ thread.title }}</h2>
      <p class="card-text">{{ thread.body }}</p>
      <small class="text-muted">By {{ thread.author.username }} | {{ thread.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
    </div>
  </div>

  <!-- Add Comment -->
  <div class="card shadow-sm rounded-3 mb-5">
    <div class="card-body">
      <h4 class="fw-semibold mb-3">Add a Comment</h4>
      <form method="POST">
        {{ form.hidden_tag() }}
        {{ form.body(class="form-control mb-2", rows="3", placeholder="Write your comment...") }}
        {{ form.submit(class="btn btn-primary") }}
      </form>
    </div>
  </div>

  <!-- Comments Section -->
  <h4 class="fw-semibold mb-3">Comments ({{ comments|length }})</h4>

{% macro render_comment(comment, level=0) %}
  {% set border_colors = ['#33cfff', '#1463F3', '#0d6efd', '#6610f2', '#6f42c1', '#d63384'] %}
  {% set bg_colors = ['#1a1a1a', '#262626', '#333333', '#1a1a1a', '#262626', '#333333'] %}
  {% set border_color = border_colors[level % border_colors|length] %}
  {% set bg_color = bg_colors[level % bg_colors|length] %}

  <div class="card mb-2 shadow-sm" style="margin-left: {{ level*20 }}px; border-left: 4px solid {{ border_color }}; background-color: {{ bg_color }};">
    <div class="card-body py-2">
      <div class="d-flex align-items-center mb-1">
        <div class="avatar bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width:30px; height:30px;">
          {{ comment.author.username[0]|upper }}
        </div>
        <strong>{{ comment.author.username }}</strong>
        <small class="text-muted ms-2">{{ comment.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
        {% if comment.replies|length > 0 %}
          <button class="btn btn-sm btn-link ms-auto" type="button" data-bs-toggle="collapse" data-bs-target="#replies-{{ comment.id }}" aria-expanded="false" aria-controls="replies-{{ comment.id }}">
            [Show Replies]
          </button>
        {% endif %}
      </div>
      <p class="mb-2">{{ comment.body }}</p>
      <form method="POST" class="mb-2">
        {{ form.hidden_tag() }}
        <input type="hidden" name="parent_id" value="{{ comment.id }}">
        {{ form.body(class="form-control mb-1", rows="2", placeholder="Reply...") }}
        {{ form.submit(class="btn btn-sm btn-outline-primary") }}
      </form>
      {% if comment.replies|length > 0 %}
        <div class="collapse" id="replies-{{ comment.id }}">
          {% for child in comment.replies %}
            {{ render_comment(child, level+1) }}
          {% endfor %}
        </div>
      {% endif %}
    </div>
  </div>
{% endmacro %}

{% for comment in comments %}
  {{ render_comment(comment) }}
{% endfor %}

</div>

<style>
  .avatar { font-size: 0.8rem; font-weight: bold; }
  .card:hover { transform: scale(1.002); transition: transform 0.1s ease-in-out; }
  /* Optional: make nested comment font slightly smaller */
  .card-body p { margin-bottom: 0.5rem; }
</style>
{% endblock %}
