{% extends "base.html" %}

{% block title %}Login - Flask Auth App{% endblock %}

{% block extra_css %}
{{ super() }}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card auth-card">
            <div class="card-header py-4 auth-header bg-primary">
                <h3 class="text-center text-white">Welcome Back</h3>
            </div>
            <div class="card-body p-5">
                <form method="POST" action="" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    <div class="mb-4">
                        <label for="username" class="form-label">Username</label>
                        {{ form.username(class="form-control") }}
                        {% for error in form.username.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">Password</label>
                        <div class="password-container">
                            {{ form.password(class="form-control", id="password") }}
                            <span class="password-toggle"><i class="fas fa-eye"></i></span>
                        </div>
                        {% for error in form.password.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-4 form-check">
                        {{ form.remember(class="form-check-input") }}
                        {{ form.remember.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-block") }}
                    </div>
                </form>
                <div class="text-center mt-4">
                    <p>Don't have an account? <a href="{{ url_for('auth.register') }}">Register here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block tb_title %} Welcome back! {% endblock %}
{% block tb_subtitle %} Login to continue {% endblock %}

{% block extra_js %}
{{ super() }}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
{% endblock %}