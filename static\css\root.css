@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap');

:root {
   /* Backgrounds */
   --bg-base: #1d2023;
   --bg-surface: #1a1a1a;
   --bg-surface-2: #262626;
   /* card base */
   --bg-surface-3: #333333;
   /* elevated card */
   --bg-surface-4: #404040;

   /* Sidebar */
   --sidebar-bg: linear-gradient(180deg, #1463F3 0%, #1d2023 100%);
   --topbar-bg: linear-gradient(90deg, #0d47a1 0%, #1a1a1a 100%);

   --topbar-shadow-color: rgba(0, 0, 0, 0.3);


   --sidebar-item-hover: rgba(255, 255, 255, 0.08);
   --sidebar-item-active: rgba(0, 174, 239, 0.15);
   --sidebar-text: #f5f5f5;
   --sidebar-text-active: #00aeef;
   --sidebar-border: rgba(255, 255, 255, 0.06);

   /* Electric blue accents */
   --accent-blue: #1463F3;
   --accent-blue-dark: #0096d6;
   --accent-blue-light: #33cfff;
   --accent-blue-soft: #80e5ff;
   --accent-blue-deep: #004c66;

   /* Cards */
   --card-bg: #26282e;
   /* slightly lighter than bg-base */
   --card-hover-bg: #33363e;
   /* on hover, subtle lift */
   --card-hover-shadow: 0 8px 25px rgba(20, 98, 243, 0.08);
   --card-border: rgba(255, 255, 255, 0.06);

   /* Text */
   --text-primary: #f5f5f5;
   --text-secondary: #c4c4c4;
   --text-muted: #5a5d63;

   /* Layout */
   --sidebar-width: 280px;
   --sidebar-collapsed-width: 80px;
   --topbar-height: 70px;
   --transition-speed: 0.3s;
   --border-radius: 10px;
}

body {
   font-family: 'Inter', sans-serif;
   background: var(--bg-base);
   color: #e9ecef;
   overflow-x: hidden;
}

form {
   background: transparent;
   padding: 25px;
   border-radius: 12px;
   max-width: 700px;
   margin: 0 auto;
}

form div {
   margin-bottom: 20px;
}

label {
   font-weight: 600;
   font-size: 14px;
   margin-bottom: 8px;
   display: block;
   color: var(--sidebar-text);
}

input[type="text"],
textarea,
select {
   width: 100%;
   padding: 12px 14px;
   border: 1px solid #d0d0d0;
   border-radius: 8px;
   font-size: 15px;
   transition: border-color 0.2s, box-shadow 0.2s;
   background-color: var(--bg-base);
   color: var(--text-primary);
}

input[type="text"]:focus,
textarea:focus,
select:focus {
   border-color: #0d6efd;
   color: var(--text-primary);
   background-color: var(--card-bg);
   box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.15);
   outline: none;
}

textarea {
   resize: vertical;
}

button,
input[type="submit"] {
   display: inline-block;
   background: #0d6efd;
   color: #fff;
   font-weight: 600;
   padding: 12px 22px;
   border: none;
   border-radius: 8px;
   cursor: pointer;
   transition: background 0.2s, transform 0.1s;
}

button:hover,
input[type="submit"]:hover {
   background: #0b5ed7;
}

button:active,
input[type="submit"]:active {
   transform: scale(0.97);
}

.password-container {
   position: relative;
}

.password-container input[type="password"] {
   width: 100%;
   padding: 12px 40px 12px 14px;
   /* extra right padding for the toggle icon */
   border: 1px solid #d0d0d0;
   border-radius: 8px;
   font-size: 15px;
   transition: border-color 0.2s, box-shadow 0.2s;
   background-color: var(--bg-base);
   color: var(--text-primary);
}

/* Focus effect */
.password-container input[type="password"]:focus {
   border-color: #0d6efd;
   background-color: var(--card-bg);
   box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.15);
   outline: none;
}

/* Password toggle icon */
.password-toggle {
   position: absolute;
   right: 12px;
   top: 50%;
   transform: translateY(-50%);
   cursor: pointer;
   color: #6c757d;
   font-size: 16px;
   transition: color 0.2s;
}

.password-toggle:hover {
   color: #0d6efd;
}

/* All placeholders */
input::placeholder,
textarea::placeholder,
select::placeholder {
   color: #bbbbbb;
   /* lighter gray for unfocused state */
   opacity: 1;
   font-style: italic;
   font-weight: 400;
   transition: color 0.2s;
}

/* Placeholder on focus */
input:focus::placeholder,
textarea:focus::placeholder,
select:focus::placeholder {
   color: #999999;
   /* slightly darker on focus for contrast */
}

input.form-control::placeholder,
textarea.form-control::placeholder,
select.form-control::placeholder {
   color: #bbbbbb;
   /* light gray for unfocused */
   opacity: 1;
   font-style: italic;
   font-weight: 400;
   transition: color 0.2s;
}

input.form-control:focus::placeholder,
textarea.form-control:focus::placeholder,
select.form-control:focus::placeholder {
   color: #999999;
   /* slightly darker on focus */
}

.pagination {
   display: flex;
   justify-content: center;
   gap: 0.5rem;
   margin-top: 2rem;
   flex-wrap: wrap;
   border: none;
}

.pagination .page-item {
   border-radius: 8px;
   overflow: hidden;
   border: none;
}

.pagination .page-link {
   display: block;
   padding: 0.45rem 0.9rem;
   color: var(--text-primary, #0d6efd);
   background-color: var(--card-bg, #fff);
   transition: all 0.2s ease;
   font-size: 0.9rem;
   border: none;
}

.pagination .page-link:hover {
   background-color: #0d6efd;
   color: #fff;
   border-color: #0d6efd;
   text-decoration: none;
}

.pagination .page-item.active .page-link {
   background-color: #0d6efd;
   color: #fff;
   border-color: #0d6efd;
}

.pagination .page-item.active .page-link:hover {
   background-color: var(--accent-blue-light);
   color: #fff;
   border-color: #0d6efd;
}

.pagination .page-item.disabled .page-link {
   color: #adb5bd;
   pointer-events: none;
   background-color: var(--card-bg, #fff);
}

/* Centered Modern Flash Alerts */
.flash-alert {
   min-width: 300px;
   max-width: 500px;
   padding: 0.85rem 1.2rem;
   margin-bottom: 10px;
   border-radius: 14px;
   box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
   font-family: 'Inter', sans-serif;
   font-weight: 500;
   font-size: 0.95rem;
   animation: slideDown 0.5s ease forwards;
   opacity: 0;
}

/* Colors for categories */
.flash-alert.alert-success {
   background: rgba(0, 255, 72, 0.1);
   color: rgba(0, 255, 72, 1);
   border: 1px solid rgba(0, 255, 72, 1);
}

.flash-alert.alert-danger {
   background: rgba(255, 0, 0, 0.1);
   color: red;
   border: 1px solid red;
}

.flash-alert.alert-warning {
   background: rgba(255, 166, 0, 0.1);
   color: orange;
   border: 1px solid orange;
}

.flash-alert.alert-info {
   background: rgba(21, 101, 192, 0.1);
   color: var(--accent-blue);
   border: 1px solid var(--accent-blue);
}

/* Slide-down animation */
@keyframes slideDown {
   0% {
      transform: translateY(-30px);
      opacity: 0;
   }

   100% {
      transform: translateY(0);
      opacity: 1;
   }
}
