{% extends "base.html" %}

{% block title %}Register - Flask Auth App{% endblock %}

{% block extra_css %}
{{ super() }}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card auth-card">
            <div class="card-header py-4 auth-header bg-primary">
                <h3 class="text-center text-white">Create Account</h3>
            </div>
            <div class="card-body p-5">
                <form method="POST" action="" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                  <div class="mb-4">
                        <label for="username" class="form-label">Username</label>
                        {{ form.username(class="form-control") }}
                        {% for error in form.username.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">Password</label>
                        <div class="password-container">
                            {{ form.password(class="form-control", id="password") }}
                            <span class="password-toggle"><i class="fas fa-eye"></i></span>
                        </div>
                        <div class="password-strength" id="password-strength-bar"></div>
                        <div class="strength-text" id="password-strength-text"></div>
                        
<ul class="validation-list mt-2" id="password-validation">
    <li id="length-check"><i class="fas fa-circle"></i> At least 8 characters</li>
    <li id="uppercase-check"><i class="fas fa-circle"></i> One uppercase letter</li>
    <li id="lowercase-check"><i class="fas fa-circle"></i> One lowercase letter</li>
    <li id="number-check"><i class="fas fa-circle"></i> One number</li>
    <li id="special-check"><i class="fas fa-circle"></i> One special character</li>
</ul>


                        
                        {% for error in form.password.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-4">
                        <label for="confirm_password" class="form-label">Confirm Password</label>
                        <div class="password-container">
                            {{ form.confirm_password(class="form-control", id="confirm_password") }}
                            <span class="password-toggle"><i class="fas fa-eye"></i></span>
                        </div>
                        <div id="confirm-validation" class="mt-1"></div>
                        {% for error in form.confirm_password.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-block") }}
                    </div>
                </form>
                <div class="text-center mt-4">
                    <p>Already have an account? <a href="{{ url_for('auth.login') }}">Sign in here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tb_title %} First time? {% endblock %}
{% block tb_subtitle %} Make an account for free! {% endblock %}

{% block extra_js %}
{{ super() }}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>

{% endblock %}