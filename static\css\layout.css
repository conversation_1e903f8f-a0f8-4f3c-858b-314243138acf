.nav-link.sub-link {
   display: flex;
   align-items: center;
   padding: 10px 16px 10px 44px;
   color: var(--sidebar-text);
   text-decoration: none;
   font-size: 14px;
   transition: all 0.2s ease;
}

.nav-link.sub-link:hover {
   background: var(--sidebar-item-hover);
   color: white;
}

.nav-label {
   text-transform: uppercase;
   font-size: 11px;
   font-weight: 600;
   letter-spacing: 0.8px;
   padding: 12px 16px 8px;
   opacity: 0.5;
}

.nav-item {
   margin-bottom: 4px;
   position: relative;
}

.nav-link {
   display: flex;
   align-items: center;
   padding: 12px 16px;
   color: var(--sidebar-text);
   text-decoration: none;
   border-radius: var(--border-radius);
   transition: all 0.2s ease;
   font-weight: 500;
}

.nav-link-logout {
   display: flex;
   align-items: center;
   padding: 12px 16px;
   color: var(--sidebar-text);
   text-decoration: none;
   border-radius: var(--border-radius);
   transition: all 0.2s ease;
   font-weight: 500;
}

.nav-link-logout:hover,
.nav-link-logout.active {
   background: rgba(255, 0, 0, 0.3);
   color: #ff1500;
}

.nav-link:hover,
.nav-link.active {
   background: var(--sidebar-item-hover);
   color: white;
}

.nav-link.active {
   color: var(--sidebar-text-active);
   background: var(--sidebar-item-active);
   font-weight: 600;
}

.nav-link.active .link-icon {
   color: var(--sidebar-text-active);
}

.link-icon {
   width: 22px;
   height: 22px;
   margin-right: 12px;
   display: flex;
   align-items: center;
   justify-content: center;
   transition: all 0.2s ease;
}

.link-text {
   flex-grow: 1;
   font-size: 14.5px;
}



.text-muted {
   color: var(--text-muted) !important;
}



h1,
h2,
h3,
h4,
h5,
h6 {
   font-family: 'Plus Jakarta Sans', sans-serif;
   font-weight: 600;
   color: var(--text-primary);
}

.muted {
   color: var(--text-muted);
}

/* Sidebar Styles */
#sidebar {
   position: fixed;
   top: 0;
   left: 0;
   height: 100vh;
   width: var(--sidebar-width);
   background: var(--sidebar-bg);
   color: var(--sidebar-text);
   transition: all var(--transition-speed) ease;
   z-index: 1000;
   display: flex;
   flex-direction: column;
}

.sidebar-header {
   padding: 24px 24px 16px;
   border-bottom: 1px solid var(--sidebar-border);
}

.sidebar-brand {
   display: flex;
   align-items: center;
   text-decoration: none;
   color: white;
}

.brand-text {
   font-family: 'Plus Jakarta Sans', sans-serif;
   font-weight: 700;
   font-size: 2rem;
   letter-spacing: 0.5px;

   background: linear-gradient(90deg, var(--accent-blue-light), var(--accent-blue-soft));
   -webkit-background-clip: text;
   -webkit-text-fill-color: transparent;

   /* Safari/Chrome */
   background-clip: text;
   /* For Firefox */
   color: transparent;
}

.brand-tagline {
   font-size: 1rem;
   opacity: 0.7;
   margin-top: 2px;
   letter-spacing: 0.5px;
   font-family: "Space Grotesk", sans-serif;
}

.sidebar-nav {
   padding: 16px 12px;
   flex-grow: 1;
   overflow-y: auto;
}







.sidebar-footer {
   padding: 16px;
   border-top: 1px solid var(--sidebar-border);
}

.user-widget {
   display: flex;
   align-items: center;
   padding: 12px;
   border-radius: var(--border-radius);
   transition: all 0.2s ease;
}

.user-widget:hover {
   background: var(--sidebar-item-hover);
}

.user-avatar {
   width: 40px;
   height: 40px;
   border-radius: 10px;
   object-fit: cover;
   margin-right: 12px;
   background: linear-gradient(135deg, #2962ff, #00b0ff);
   display: flex;
   align-items: center;
   justify-content: center;
   color: white;
   font-weight: 600;
}

.user-info {
   flex-grow: 1;
}

.user-name {
   font-weight: 600;
   font-size: 14.5px;
   margin-bottom: 2px;
}

.user-role {
   font-size: 12px;
   opacity: 0.7;
}

/* Content Area */
#content {
   margin-left: var(--sidebar-width);
   transition: margin-left var(--transition-speed) ease;
   min-height: 100vh;
}

.topbar {
   height: var(--topbar-height);
   /* background: #050505; */
   background: var(--topbar-bg);
   color: white;
   box-shadow: 0 2px 10px var(--topbar-shadow-color);
   display: flex;
   align-items: center;
   justify-content: space-between;
   padding: 0 24px;
}

.page-title h1 {
   font-size: 24px;
   font-weight: 700;
   margin-bottom: 4px;
   color: white;
}

.page-title p {
   color: gainsboro;
   margin: 0;
   font-size: 14px;
}

.main-content {
   padding: 24px;
}



/* Toggle button for demo purposes */
.toggle-sidebar {
   position: fixed;
   bottom: 20px;
   right: 20px;
   z-index: 1001;
   width: 50px;
   height: 50px;
   border-radius: 50%;
   background: #2962ff;
   color: white;
   display: flex;
   align-items: center;
   justify-content: center;
   box-shadow: 0 4px 12px rgba(41, 98, 255, 0.3);
   cursor: pointer;
}



/* Accordion styles for sidebar */
.accordion-button {
   background: transparent;
   border: none;
   padding: 12px 16px;
   font-weight: 500;
   color: var(--sidebar-text);
   display: flex;
   align-items: center;
   box-shadow: none !important;
   transition: all 0.2s ease;
}

.accordion-button:hover {
   background: var(--sidebar-item-hover);
   color: white;
}


.accordion-button:not(.collapsed) {
   background: var(--sidebar-item-active);
   color: var(--sidebar-text-active);

}

.accordion-button:focus {
   box-shadow: none;
}

.accordion-button::after {
   margin-left: auto;
   transition: transform 0.2s ease;
   filter: brightness(0.8);
   /* Adjust icon color for dark theme */
}

.accordion-item {
   background: transparent;
   border: none;
}

.accordion-collapse {
   transition: height 0.3s ease;
   color: var(--sidebar-text);
}