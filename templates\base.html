<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Sidebar Design</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar" class="d-none d-lg-flex flex-column">
        <div class="sidebar-header">
            <a href="/" class="sidebar-brand">
                <div>
                    <div class="brand-text">DevSphere.run</div>
                    <div class="brand-tagline"><i>Your Dev Universe.</i> [BETA]</div>
                </div>
            </a>
        </div>
        
        <div class="sidebar-nav accordion accordion-flush" id="sidebarAccordion">

            <div class="nav-label">Main</div>
            <div class="nav-item">
                <a href="/" class="nav-link active">
                    <div class="link-icon">
                        <i class="bi bi-house"></i>
                    </div>
                    <div class="link-text">Home</div>
                </a>
            </div>

            <div class="nav-item">
                <a href="#" class="nav-link active">
                    <div class="link-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <div class="link-text">Search</div>
                </a>
            </div>
            
           <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="link-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <div class="link-text">About</div>
                     <!--<span class="badge bg-primary ms-2">New</span>-->
                </a>
            </div>
            
            <div class="nav-label">Content</div>
            <div class="accordion-item nav-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#contentSubmenu" aria-expanded="false" aria-controls="contentSubmenu">
                        <div class="link-icon me-2">
                            <i class="bi bi-collection"></i>
                        </div>
                        <div class="link-text">Snippets</div>
                    </button>
                </h2>
                <div id="contentSubmenu" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                    <div class="accordion-body p-0">
                        <a class="nav-link sub-link" href="{{ url_for('snippets.explore') }}">Explore</a>
                        <a class="nav-link sub-link" href="{{ url_for('snippets.create') }}">Create</a>
                        <a class="nav-link sub-link" href="{{ url_for('snippets.saved_snippets') }}">Liked</a>
                    </div>
                </div>
            </div>
            
            <div class="nav-label">Community</div>
            <div class="accordion-item nav-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#communitySubmenu" aria-expanded="false" aria-controls="communitySubmenu">
                        <div class="link-icon me-2">
                            <i class="bi bi-chat-right-text"></i>
                        </div>
                        <div class="link-text">Threads</div>
                    </button>
                </h2>
                <div id="communitySubmenu" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                    <div class="accordion-body p-0">
                        <a class="nav-link sub-link" href="{{ url_for('thread.threads') }}">Explore</a>
                        <a class="nav-link sub-link" href="{{ url_for('thread.new_threads') }}">Start Thread</a>
                        <a class="nav-link sub-link" href="{{ url_for('thread.threads') }}">My threads</a>
                    </div>
                </div>
            </div>

            <div class="nav-label">Settings</div>
            <div class="accordion-item nav-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#settingsSubmenu" aria-expanded="false" aria-controls="settingsSubmenu">
                        <div class="link-icon me-2">
                            <i class="bi bi-gear"></i>
                        </div>
                        <div class="link-text">Settings</div>
                    </button>
                </h2>
                <div id="settingsSubmenu" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                    <div class="accordion-body p-0">
                        <a class="nav-link sub-link" href="#">General</a>
                        <a class="nav-link sub-link" href="#">Security</a>
                        <a class="nav-link sub-link" href="#">Notifications</a>
                    </div>
                </div>
            </div>
            {% if current_user.is_authenticated %}
            <div class="nav-item">
                <a href="{{ url_for('auth.logout') }}" class="nav-link-logout">
                    <div class="link-icon">
                        <i class="bi bi-box-arrow-left"></i>
                    </div>
                    <div class="link-text">Logout</div>
                </a>
            </div>
            {% else %}
            <div class="nav-item">
                <a href="{{ url_for('auth.login') }}" class="nav-link">
                    <div class="link-icon">
                        <i class="bi bi-box-arrow-right"></i>
                    </div>
                    <div class="link-text">Login</div>
                </a>
            </div>
            {% endif %}
        </div>
        
        <div class="sidebar-footer">
            <div class="user-widget">
                {% if current_user.is_authenticated %}
                <div class="user-avatar">{{ current_user.username[0] | upper }}</div>
                <div class="user-info">
                    <div class="user-name">{{ current_user.username }}</div>
                    <div class="user-role">{{ current_user.role_display  }}</div>
                </div>
                {% else %}
                <div class="user-avatar">G</div>
                <div class="user-info">
                    <div class="user-name">Guest</div>
                    <div class="user-role">Administrator</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Content -->
    <div id="content">
        <div class="topbar">
            <div class="page-title">
                <h1>{% block tb_title %} Welcome {{ current_user.username }}! {% endblock %}</h1>
                <p>{% block tb_subtitle %} Your ultimate dev Universe. {% endblock %}</p>
            </div>
            <div class="topbar-actions">
                <button class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Add snippet
                </button>
            </div>
        </div>
<!-- Centered Flash Notifications -->
<div class="flash-container-top text-center position-fixed top-3 start-50 translate-middle-x" style="z-index: 1055;">
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="flash-alert alert alert-{{ category }} alert-dismissible fade show d-inline-flex align-items-center" role="alert">
                <i class="bi me-2" data-bs-icon="{{ category }}"></i>
                <span>{{ message }}</span>
                <button type="btn-close" class="btn-close ms-2" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
</div>


        <div class="main-content">
            
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Toggle Sidebar Button (for demo) -->
    <div class="toggle-sidebar d-lg-none" id="toggleSidebar">
        <i class="bi bi-list"></i>
    </div>


    <!-- Bootstrap & Popper.js -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>

<script src="{{ url_for('static', filename='js/script.js') }}"></script>
    {% block extra_js %}{% endblock %}

</body>
</html>