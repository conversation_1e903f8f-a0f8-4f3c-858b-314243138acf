from utils import build_comment_tree
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, flash, abort
from flask_login import login_user, logout_user, current_user, login_required
from models import db, User, Thread, Comment
from forms import ThreadForm, CommentForm

thread_bp = Blueprint('thread', __name__)

@thread_bp.route('/threads')
def threads():
    threads = Thread.query.order_by(Thread.created_at.desc()).all()
    return render_template('threads/threads.html', threads=threads)

@thread_bp.route('/thread/new', methods=['GET', 'POST'])
@login_required
def new_threads():
    form = ThreadForm()
    if form.validate_on_submit():
        thread = Thread(title=form.title.data, body=form.body.data, author=current_user)
        db.session.add(thread)
        db.session.commit()
        flash('Thread created!', 'success')
        return redirect(url_for('main.home'))
    return render_template('threads/thread.html', form=form, new=True)

@thread_bp.route('/thread/<int:thread_id>', methods=['GET', 'POST'])
def view_thread(thread_id):
    thread = Thread.query.get_or_404(thread_id)
    form = CommentForm()
    if form.validate_on_submit() and current_user.is_authenticated:
        parent_id = request.form.get('parent_id')
        comment = Comment(
            thread=thread,
            author=current_user,
            body=form.body.data,
            parent_id=parent_id if parent_id else None
        )
        db.session.add(comment)
        db.session.commit()
        return redirect(url_for('thread.view_thread', thread_id=thread.id))
    comments = build_comment_tree(thread.comments.all())
    return render_template('threads/thread.html', thread=thread, form=form, comments=comments, new=False)