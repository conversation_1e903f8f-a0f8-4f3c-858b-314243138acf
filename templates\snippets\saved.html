{% extends "base.html" %}

{% block title %}My Saved Snippets{% endblock %}

{% block content %}
<div class="container my-5">

    <h2 class="mb-4"></h2>

    <div class="row">
        {% for snippet in snippets %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card snippet-card h-100 shadow-sm border-0">
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ snippet.title }}</h5>
                    <p class="card-text text-truncate mb-2">{{ snippet.description }}</p>
                    <p class="mb-1"><strong>Language:</strong> {{ snippet.language }}</p>
                    <p class="mb-1"><strong>By:</strong> {{ snippet.author.username }}</p>
                    <p>
                        {% for tag in snippet.tags %}
                            <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                        {% endfor %}
                    </p>
                    <div class="mt-auto d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('snippets.detail', snippet_id=snippet.id) }}" class="btn btn-sm btn-outline-primary">View</a>

                        <!-- Remove from saved -->
                        <form method="POST" action="{{ url_for('snippets.remove_saved', snippet_id=snippet.id) }}">
                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash-alt"></i> Remove
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <p class="text-muted">You have not saved any snippets yet.</p>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block tb_title %} My Liked Snippets {% endblock %}
{% block tb_subtitle %} {{ current_user.username }} {% endblock %}