from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, flash, abort
from functools import wraps
from flask_login import login_user, logout_user, current_user, login_required
from models import Snippet, Tag, db, snippet_views
from datetime import datetime
from forms import SnippetForm
import humanize
from utils import admin_required
from sqlalchemy import func, outerjoin, or_
import bleach

ALLOWED_TAGS = [
    "a", "b", "i", "u", "em", "strong", "p", "br",
    "ul", "ol", "li", "pre", "code", "blockquote"
]
ALLOWED_ATTRS = {"a": ["href", "title", "target"]}

def clean_html(value):
    return bleach.clean(value, tags=ALLOWED_TAGS, attributes=ALLOWED_ATTRS, strip=True)

snippets_bp = Blueprint('snippets', __name__)

@snippets_bp.route("/create", methods=["GET", "POST"])
@login_required
def create():
    form = SnippetForm()
    if form.validate_on_submit():
        snippet = Snippet(
            title=form.title.data,
            description=clean_html(form.description.data),
            code=form.code.data,
            language=form.language.data,
            author=current_user
        )
        # handle tags
        tag_names = [t.strip().lower() for t in form.tags.data.split(",") if t.strip()]
        snippet.tags = []
        for name in tag_names:
            tag = Tag.query.filter_by(name=name).first()
            if not tag:
                tag = Tag(name=name)
                db.session.add(tag)
            snippet.tags.append(tag)
        db.session.add(snippet)
        db.session.commit()
        return redirect(url_for("snippets.detail", snippet_id=snippet.id))
    return render_template("snippets/create.html", form=form)
 
@snippets_bp.route("/<int:snippet_id>")
def detail(snippet_id):
    snippet = Snippet.query.get_or_404(snippet_id)

    # Only record a view if user is logged in or anonymous
    db.session.execute(
        snippet_views.insert().values(
            user_id=current_user.id if current_user.is_authenticated else None,
            snippet_id=snippet.id,
            viewed_at=datetime.utcnow()
        )
    )
    db.session.commit()

    return render_template("snippets/detail.html", snippet=snippet)


@snippets_bp.route('/explore')
def explore():
    search_query = request.args.get('q', '').strip()
    sort_by = request.args.get('sort', 'newest')
    page = request.args.get('page', 1, type=int)
    per_page = 9  # cards per page

    # Subquery: total views per snippet
    view_counts = (
        db.session.query(
            snippet_views.c.snippet_id,
            func.count(snippet_views.c.id).label("view_count")
        )
        .group_by(snippet_views.c.snippet_id)
    ).subquery()

    # Query Snippets + view_count
    snippets_query = db.session.query(Snippet, view_counts.c.view_count).outerjoin(
        view_counts, Snippet.id == view_counts.c.snippet_id
    )

    # Search
    if search_query:
        snippets_query = snippets_query.join(Snippet.tags, isouter=True).filter(
            db.or_(
                Snippet.title.ilike(f'%{search_query}%'),
                Snippet.description.ilike(f'%{search_query}%'),
                Tag.name.ilike(f'%{search_query}%')
            )
        )

    # Sorting
    if sort_by == 'newest':
        snippets_query = snippets_query.order_by(Snippet.created_at.desc())
    elif sort_by == 'oldest':
        snippets_query = snippets_query.order_by(Snippet.created_at.asc())
    elif sort_by == 'title_asc':
        snippets_query = snippets_query.order_by(Snippet.title.asc())
    elif sort_by == 'title_desc':
        snippets_query = snippets_query.order_by(Snippet.title.desc())

    # Group by for aggregation
    snippets_query = snippets_query.group_by(Snippet.id)

    # Pagination using Flask-SQLAlchemy
    snippets_paginated = snippets_query.paginate(page=page, per_page=per_page, error_out=False)

    # Attach view_count to snippet objects for template
    for s, count in snippets_paginated.items:
        s.view_count_tmp = count or 0

    

    return render_template(
        'snippets/explore.html',
        snippets=[s for s, _ in snippets_paginated.items],
        pagination=snippets_paginated,
        search_query=search_query,
        sort_by=sort_by
    )


@snippets_bp.route('/snippets/<int:snippet_id>/save', methods=['POST'])
@login_required
def save(snippet_id):
    snippet = Snippet.query.get_or_404(snippet_id)
    if current_user not in snippet.saved_by:
        snippet.saved_by.append(current_user)
        db.session.commit()
        flash("Snippet saved to your favourites!", "success")
    else:
        flash("Snippet already saved.", "info")
    return redirect(request.referrer or url_for('snippets.explore'))

@snippets_bp.route('/saved')
@login_required
def saved_snippets():
    snippets = current_user.saved_snippets  # relationship defined in User model
    return render_template('snippets/saved.html', snippets=snippets)

# Remove snippet from saved
@snippets_bp.route('/saved/<int:snippet_id>/remove', methods=['POST'])
@login_required
def remove_saved(snippet_id):
    snippet = Snippet.query.get_or_404(snippet_id)
    if snippet in current_user.saved_snippets:
        current_user.saved_snippets.remove(snippet)
        db.session.commit()
        flash("Snippet removed from your saved list.", "success")
    else:
        flash("Snippet not in your saved list.", "info")
    return redirect(request.referrer or url_for('snippets.saved_snippets'))

def get_or_create_tag(name):
    name = name.strip()
    if not name:
        return None

    tag = Tag.query.filter_by(name=name).first()
    if tag:
        return tag

    # Tag doesn’t exist → create it
    tag = Tag(name=name)
    db.session.add(tag)
    db.session.flush()  # ensure it gets an ID without committing
    return tag

@snippets_bp.route('/snippets/<int:snippet_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(snippet_id):
    snippet = Snippet.query.get_or_404(snippet_id)

    # Only author can edit
    if snippet.author != current_user:
        flash("You do not have permission to edit this snippet.", "danger")
        return redirect(url_for('snippets.detail', snippet_id=snippet.id))

    form = SnippetForm(obj=snippet)

    # Pre-fill tags as comma-separated string (only on GET)
    if request.method == 'GET':
        form.tags.data = ", ".join(tag.name for tag in snippet.tags)

    if form.validate_on_submit():
        snippet.title = form.title.data
        snippet.description = form.description.data
        snippet.code = form.code.data
        snippet.language = form.language.data

        # Convert comma-separated string back into Tag objects
        tag_names = [t.strip() for t in form.tags.data.split(",") if t.strip()]
        snippet.tags = [get_or_create_tag(name) for name in tag_names]

        db.session.commit()
        flash("Snippet updated successfully.", "success")
        return redirect(url_for('snippets.detail', snippet_id=snippet.id))

    return render_template('snippets/create.html', form=form, snippet=snippet, edit=True)

@snippets_bp.route("/snippet/<int:snippet_id>/feature", methods=["POST"])
@login_required
@admin_required
def feature_snippet(snippet_id):
    snippet = Snippet.query.get_or_404(snippet_id)
    snippet.featured = not snippet.featured  # toggle featured status
    db.session.commit()
    flash(f"Snippet '{snippet.title}' marked as {'featured' if snippet.featured else 'not featured'}.", "success")
    return redirect(request.referrer or url_for("main.home"))