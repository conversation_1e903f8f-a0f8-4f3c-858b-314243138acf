// Toggle sidebar on mobile
document.getElementById('toggleSidebar').addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('active');
    
    // Toggle icon between menu and close
    const icon = this.querySelector('i');
    if (sidebar.classList.contains('active')) {
        icon.classList.remove('bi-list');
        icon.classList.add('bi-x');
    } else {
        icon.classList.remove('bi-x');
        icon.classList.add('bi-list');
    }
});

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('toggleSidebar');
    
    if (window.innerWidth < 992 && 
        !sidebar.contains(event.target) && 
        !toggleBtn.contains(event.target) &&
        sidebar.classList.contains('active')) {
        sidebar.classList.remove('active');
        toggleBtn.querySelector('i').classList.remove('bi-x');
        toggleBtn.querySelector('i').classList.add('bi-list');
    }
});

// Add smooth scrolling to sidebar links
document.querySelectorAll('.sidebar-nav a').forEach(link => {
    link.addEventListener('click', function(e) {
        // Only apply to hash links
        if (this.getAttribute('href').startsWith('#')) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    });
});

// Add active class to current page link
function setActivePage() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    document.querySelectorAll('.sidebar-nav a').forEach(link => {
        const linkPage = link.getAttribute('href');
        if (linkPage === currentPage || (currentPage === '' && linkPage === 'index.html')) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    setActivePage();
    
    // Add slight animation to cards on page load
    setTimeout(() => {
        document.querySelectorAll('.card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 * index);
        });
    }, 100);
});

// Handle window resize events
window.addEventListener('resize', function() {
    if (window.innerWidth >= 992) {
        // On desktop, ensure sidebar is visible
        document.getElementById('sidebar').classList.remove('active');
        const toggleIcon = document.getElementById('toggleSidebar').querySelector('i');
        toggleIcon.classList.remove('bi-x');
        toggleIcon.classList.add('bi-list');
    }
});

document.addEventListener('DOMContentLoaded', () => {
    const alerts = document.querySelectorAll('.flash-alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            bootstrap.Alert.getOrCreateInstance(alert).close();
        }, 5000); // 5 seconds
    });
});

document.querySelectorAll('.flash-alert').forEach(alert => {
    const icon = alert.querySelector('i.bi');
    if(alert.classList.contains('alert-success')) icon.classList.add('bi-check-circle');
    if(alert.classList.contains('alert-danger'))  icon.classList.add('bi-exclamation-octagon');
    if(alert.classList.contains('alert-warning')) icon.classList.add('bi-exclamation-triangle');
    if(alert.classList.contains('alert-info'))    icon.classList.add('bi-info-circle');
});
