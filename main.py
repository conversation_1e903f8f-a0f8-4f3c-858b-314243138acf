from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, flash, abort
from functools import wraps
from flask_login import login_user, logout_user, current_user, login_required
from models import db, User, Snippet, saved, snippet_views
import humanize
from sqlalchemy import func,case
from datetime import datetime, timedelta
from utils import admin_required

main_bp = Blueprint('main', __name__)



@main_bp.route('/landing')
def index():
    return render_template('index.html')

def get_top_snippets_by_likes(period="day", limit=5):
    now = datetime.utcnow()
    since = None

    if period == "day":
        since = now - timedelta(days=1)
    elif period == "week":
        since = now - timedelta(weeks=1)
    elif period == "month":
        since = now - timedelta(days=30)
    elif period == "year":
        since = now - timedelta(days=365)

    # Count only saves in the period (or all if since is None)
    save_count_expr = func.count(
        case((saved.c.saved_at >= since, 1))
 if since else saved.c.snippet_id
    ).label("save_count")

    query = (
        db.session.query(Snippet)
        .add_columns(save_count_expr)
        .outerjoin(saved, Snippet.id == saved.c.snippet_id)
        .group_by(Snippet.id)
        .order_by(save_count_expr.desc())
        .limit(limit)
    )

    return query.all()


def get_top_snippets_by_views(period="day", limit=5):
    query = (
        db.session.query(
            Snippet,
            func.count(snippet_views.c.id).label("view_count")  # count all rows
        )
        .outerjoin(snippet_views, Snippet.id == snippet_views.c.snippet_id)  # include snippets with 0 views
    )

    if period != "all":
        if period == "day":
            since = datetime.utcnow() - timedelta(days=1)
        elif period == "week":
            since = datetime.utcnow() - timedelta(weeks=1)
        elif period == "month":
            since = datetime.utcnow() - timedelta(days=30)
        elif period == "year":
            since = datetime.utcnow() - timedelta(days=365)
        query = query.filter(snippet_views.c.viewed_at >= since)

    return (
        query.group_by(Snippet.id)
        .order_by(func.count(snippet_views.c.id).desc())
        .limit(limit)
        .all()
    )


@main_bp.route('/')
def home():
    period = request.args.get("period", "all")
    metric = request.args.get("metric", "likes")
    
    if metric == "views":
        top_snippets = get_top_snippets_by_views(period)
    else:
        top_snippets = get_top_snippets_by_likes(period)


    total_users = User.query.count()
    pretty_users = humanize.intcomma(total_users)
    short_users = humanize.intword(total_users)
    
    total_snippets = Snippet.query.count()
    pretty_snippets = humanize.intcomma(total_snippets)
    short_snippets = humanize.intword(total_snippets)
    
    featuerd_snippet = Snippet.query.filter_by(featured=True).all()
    
    return render_template('home.html', total_users=pretty_users, total_snippets=pretty_snippets, top_snippets=top_snippets,
                           period=period, metric=metric, featuerd_snippet=featuerd_snippet)

@main_bp.route('/profile')
def profile():
    return render_template('profile.html')

@main_bp.route('/admin', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_panel():
    
    users = User.query.order_by(User.username).all()
    return render_template('admin/dash.html', users=users)