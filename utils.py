from functools import wraps
from flask import abort, flash, redirect, url_for
from flask_login import current_user

def role_required(role_name):
    """Restrict route access to users with a specific role"""
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            if not current_user.is_authenticated:
                abort(401)  # not logged in
            if current_user.role.name != role_name:
                abort(403)  # forbidden
            return f(*args, **kwargs)
        return wrapper
    return decorator

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.role != 'admin':  # single-role version
            flash("Access denied.", "danger")
            return redirect(url_for('home'))
        return f(*args, **kwargs)
    return decorated_function

def build_comment_tree(comments):
    comment_dict = {c.id: c for c in comments}
    tree = []

    for comment in comments:
        comment.replies = []
    for comment in comments:
        if comment.parent_id:
            parent = comment_dict.get(comment.parent_id)
            parent.replies.append(comment)
        else:
            tree.append(comment)
    return tree