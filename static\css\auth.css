:root {
   --primary-color: #4e73df;
   --secondary-color: #858796;
   --success-color: #1cc88a;
   --danger-color: #e74a3b;
   --warning-color: #f6c23e;
   --light-bg: #f8f9fc;
}

.auth-card {
   border: 0;
   border-radius: 1rem;
   box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.auth-header {
   border-top-left-radius: 1rem;
   border-top-right-radius: 1rem;
}

.form-control {
   border-radius: 0.35rem;
   padding: 0.75rem 1rem;
   font-size: 0.9rem;
}

.form-control:focus {
   box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}


.password-toggle {
   cursor: pointer;
   position: absolute;
   right: 15px;
   top: 50%;
   transform: translateY(-50%);
   color: var(--secondary-color);
}

.password-container {
   position: relative;
}

.password-strength {
   height: 5px;
   margin-top: 5px;
   border-radius: 3px;
   transition: all 0.3s ease;
}

.strength-weak {
   background-color: var(--danger-color);
   width: 25%;
}

.strength-medium {
   background-color: var(--warning-color);
   width: 50%;
}

.strength-strong {
   background-color: var(--success-color);
   width: 100%;
}

.strength-text {
   font-size: 0.8rem;
   margin-top: 5px;
}

.validation-list li {
   margin-bottom: 3px;
   font-size: 0.85rem;
   display: flex;
   align-items: center;
}

.validation-list li i {
   margin-right: 8px;
}

/* Valid / Invalid colors */
.validation-list li.valid {
   color: #1cc88a;
   /* green */
}

.validation-list li.invalid {
   color: #e74a3b;
   /* red */
}

.validation-list li i {
   margin-right: 8px;
}