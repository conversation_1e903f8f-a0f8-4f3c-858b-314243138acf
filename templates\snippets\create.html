{% extends "base.html" %}

{% block title %}Login - Flask Auth App{% endblock %}

{% block extra_css %}
{{ super() }}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="row justify-content-center">

    <form method="POST"
        action="{% if edit %}{{ url_for('snippets.edit', snippet_id=snippet.id) }}{% else %}{{ url_for('snippets.create') }}{% endif %}"
        class='card snippet-card'>

        {{ form.hidden_tag() }}


        <div>
            {{ form.title.label }}<br>
            {{ form.title(size=50) }}
        </div>

        <div>
            {{ form.description.label }}<br>
            {{ form.description(rows=3, cols=60) }}
        </div>

        <div>
            {{ form.code.label }}<br>
            {{ form.code(rows=10, cols=80, placeholder="print('Hello world!')") }}
        </div>

        <div>
            {{ form.language.label }}<br>
            {{ form.language() }}
        </div>

        <div>
            {{ form.tags.label }}<br>
            {{ form.tags(size=60, placeholder="e.g. flask, api, auth") }}
        </div>

        <div>
            {{ form.submit() }}
        </div>
    </form>

    {{ ckeditor.load() }}
    {% block extra_js %}
     <script src="{{ url_for('static', filename='js/auth.js') }}"></script>
<script>
    // Apply the dark theme CSS to all CKEditor instances
    CKEDITOR.on('instanceReady', function(evt) {
        var editor = evt.editor;
        editor.config.contentsCss = "{{ url_for('static', filename='css/ckeditor.css') }}";
    });
</script>

{% endblock %}

    {{ ckeditor.config(name='description') }}

    {% endblock %}

    {% block tb_title %} Create a snippet {% endblock %}
    {% block tb_subtitle %} Share your code! {% endblock %}
