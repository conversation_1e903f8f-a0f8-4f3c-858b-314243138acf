{% extends "base.html" %}

{% block title %}Home - DevSphere.run{% endblock %}

{% block page_subtitle %}Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">Stats</h2>
    </div>
</div>

<div class="row">
    <div class="col-xl-3 col-md-6 col-12">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted fw-semibold">Total Users</h6>
                        <h3 class="mb-0">{{ total_users }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="bi bi-people-fill text-primary" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 col-12">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted fw-semibold"> Total Snippets </h6>
                        <h3 class="mb-0">{{total_snippets}}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="bi bi-journal-code text-success" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 col-12">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted fw-semibold">Total Threads</h6>
                        <h3 class="mb-0">9.8K+</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="bi bi-upload text-info" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 col-12">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted fw-semibold">Guides</h6>
                        <h3 class="mb-0">1.3K+</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="bi bi-patch-question text-warning" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-3 flex-wrap">
            <h2 class="mb-0">Featured Snippets</h2>
        </div>
    </div>

    {% for snippet in featuerd_snippet %}
    <div class="col-xl-2 col-md-4 col-6 m-2">
        <div class="card m-2">
            <div class="card-body m-2">
                <h6 class="fw-bold  m-2">{{ snippet.title }}</h6>
                <p class=" m-2">{{ snippet.description[:50] }}{% if snippet.description|length > 50 %}...{% endif %}</p>

                <div class="flex-wrap m-2">
                    <a href="{{ url_for('snippets.detail', snippet_id=snippet.id) }}" class="btn-ghost">
                        <i class="bi bi-eye"></i> View
                    </a>
                </div>

                <div class="tags flex-wrap m-2">
                    {% for tag in snippet.tags %}
                    <span class="tag my-1">{{ tag.name }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-3 flex-wrap">
            <h2 class="mb-0">Top 5 Snippets</h2>
            <form method="get" class="d-flex gap-2 mb-0">
                <select name="period" onchange="this.form.submit()" class="form-select form-select-sm w-auto">
                    <option value="day" {% if period=='day' %}selected{% endif %}>Today</option>
                    <option value="week" {% if period=='week' %}selected{% endif %}>This Week</option>
                    <option value="month" {% if period=='month' %}selected{% endif %}>This Month</option>
                    <option value="year" {% if period=='year' %}selected{% endif %}>This Year</option>
                    <option value="all" {% if period=='all' %}selected{% endif %}>All Time</option>
                </select>

                <select name="metric" onchange="this.form.submit()" class="form-select form-select-sm w-auto">
                    <option value="likes" {% if metric=='likes' %}selected{% endif %}>Likes</option>
                    <option value="views" {% if metric=='views' %}selected{% endif %}>Views</option>
                </select>
            </form>

        </div>
    </div>

    {% for snippet, count in top_snippets %}
    <div class="col-xl-2 col-md-4 col-6 m-2">
        <div class="card dashboard-card">
            <div class="card-body m-2">
                <h6 class="fw-bold  m-2">{{ snippet.title }}</h6>
                <p class="m-2">{{ snippet.description[:50] }}{% if snippet.description|length > 50 %}...{% endif %}</p>
                <div class="m-2">
                    {% if metric == 'likes' %}
                    <small><i class="bi bi-heart-fill m-2"></i> {{ count }} saves</small>
                    {% else %}
                    <small><i class="bi bi-eye m-2"></i> {{ count }} views</small>
                    {% endif %}
                    <div class="flex-wrap m-2">
                        <a href="{{ url_for('snippets.detail', snippet_id=snippet.id) }}" class="btn-ghost">
                            <i class="bi bi-eye"></i> View
                        </a>
                    </div>
                </div>
                <div class="tags flex-wrap m-2">
                    {% for tag in snippet.tags %}
                    <span class="tag m-2">{{ tag.name }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>


{% endblock %}

{% block extra_js %}
<!-- Chart.js -->


<script>

</script>
{% endblock %}