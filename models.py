from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(120), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    role = db.Column(db.String(50), default='user', nullable=False) 
    password_hash = db.Column(db.String(128), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)

    threads = db.relationship('Thread', backref='author', lazy=True)
    comments = db.relationship('Comment', backref='author', lazy=True)

    snippets = db.relationship('Snippet', backref='author', lazy=True)
    saved_snippets = db.relationship(
        'Snippet',
        secondary='saved',
        back_populates='saved_by'
    )



    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def get_id(self):
        return self.id
    
    def __repr__(self):
        return f'<User {self.email}>'

    @property
    def role_display(self):
        return {
            'user': 'User',
            'admin': 'Administrator'
        }.get(self.role, self.role.title())

class LoginAttempt(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'))
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    attempted_at = db.Column(db.DateTime, default=datetime.utcnow)
    success = db.Column(db.Boolean, default=False)
    
    user = db.relationship('User', backref=db.backref('login_attempts', lazy=True))

snippet_tags = db.Table('snippet_tags',
    db.Column('snippet_id', db.Integer, db.ForeignKey('snippet.id')),
    db.Column('tag_id', db.Integer, db.ForeignKey('tag.id'))
)

class Tag(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    snippets = db.relationship(
        'Snippet',
        secondary=snippet_tags,
        back_populates='tags'
    )

saved = db.Table(
    'saved',
    db.Column('user_id', db.Integer, db.ForeignKey('user.id')),
    db.Column('snippet_id', db.Integer, db.ForeignKey('snippet.id')),
    db.Column('saved_at', db.DateTime, default=datetime.utcnow)
)

snippet_views = db.Table(
    "snippet_views",
    db.Column("id", db.Integer, primary_key=True),
    db.Column("user_id", db.Integer, db.ForeignKey("user.id")),
    db.Column("snippet_id", db.Integer, db.ForeignKey("snippet.id")),
    db.Column("viewed_at", db.DateTime, default=datetime.utcnow)
)


class Snippet(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    code = db.Column(db.Text, nullable=False)
    
    featured = db.Column(db.Boolean, default=False)
    language = db.Column(db.String(50), nullable=False)  # Python, JS, etc.
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    views = db.relationship(
    "User",
    secondary=snippet_views,
    backref="viewed_snippets"
)

    
    tags = db.relationship(
        'Tag',
        secondary=snippet_tags,
        back_populates='snippets'
    )
    saved_by = db.relationship(
        'User',
        secondary='saved',
        back_populates='saved_snippets'
    )

    views = db.relationship(
        "User",
        secondary=snippet_views,
        backref="viewed_snippets"
    )

    @property
    def save_count(self):
        return len(self.saved_by)

    @property
    def view_count(self):
        return len(self.views)


class Thread(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    author_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    comments = db.relationship('Comment', backref='thread', lazy='dynamic')


class Comment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    body = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    author_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    thread_id = db.Column(db.Integer, db.ForeignKey('thread.id'), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('comment.id'), nullable=True)
    children = db.relationship(
        'Comment',
        backref=db.backref('parent', remote_side=[id]),
        lazy='select'
    )