/* Card */
.snippet-card {
   background: rgba(255, 255, 255, 0.05);
   backdrop-filter: blur(12px);
   border-radius: 1.25rem;
   border: 1px solid rgba(255, 255, 255, 0.15);
   transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.snippet-card:hover {
   transform: translateY(-6px);
   box-shadow: 0 16px 40px rgba(0, 0, 0, 0.25);
}

/* Language badge */
.lang-badge {
   background: linear-gradient(135deg, #4facfe, #00f2fe);
   color: #fff;
   font-size: 1rem;
   /* bigger text */
   font-weight: 700;
   /* bolder */
   padding: 0.5rem 1rem;
   /* larger pill */
   border-radius: 1rem;
   transition: transform 0.25s ease;
}

.lang-badge:hover {
   transform: scale(1.2);
}

/* Code block */
.code-block {
   background: #0d1117;
   border-radius: 0.8rem;
   overflow: hidden;
   padding: 1rem;
   position: relative;
}

.code-block pre {
   margin: 0;
   font-size: 0.9rem;
   line-height: 1.5;
   color: #c9d1d9;
   transition: background 0.3s ease;
}

.code-block pre:hover {
   background: #161b22;
}

/* Copy button */
.copy-btn {
   position: absolute;
   top: 0.6rem;
   right: 0.6rem;
   background: rgba(255, 255, 255, 0.1);
   border: none;
   color: #fff;
   font-size: 0.9rem;
   padding: 0.4rem 0.6rem;
   border-radius: 0.5rem;
   cursor: pointer;
   transition: background 0.25s ease, transform 0.2s ease;
}

.copy-btn:hover {
   background: rgba(0, 198, 255, 0.3);
   transform: scale(1.05);
}

.copy-btn:active {
   transform: scale(0.95);
}

/* Tags */
.tag {
   display: inline-block;
   background: rgba(0, 198, 255, 0.15);
   color: #00c6ff;
   font-size: 0.75rem;
   padding: 0.35rem 0.7rem;
   border-radius: 0.75rem;
   margin: 0 0.3rem 0.3rem 0;
   transition: background 0.25s ease, transform 0.25s ease;
}

.tag:hover {
   background: rgba(0, 198, 255, 0.3);
   transform: translateY(-2px);
}

/* Top action buttons */
.snippet-card .btn {
   transition: transform 0.2s ease, background 0.25s ease;
}

.snippet-card .btn:hover {
   transform: translateY(-2px);
   opacity: 0.9;
}