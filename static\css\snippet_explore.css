.snippet-explore-card {
   background: rgba(255, 255, 255, 0.08);
   backdrop-filter: blur(12px);
   border-radius: 1.25rem;
   border: 1px solid rgba(255, 255, 255, 0.15);
   box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
   transition: transform 0.3s ease, box-shadow 0.3s ease !important;
   overflow: hidden;
   position: relative;
}

.snippet-explore-card:hover {
   transform: translateY(-8px) scale(1.02);
   box-shadow: 0 20px 45px rgba(0, 0, 0, 0.25);
}

/* Accent bar with animated gradient */
.snippet-explore-card .accent-bar {
   height: 4px;
   width: 100%;
   background: linear-gradient(90deg, #007bff, #00c6ff, #007bff);
   background-size: 200% 200%;
   animation: gradientMove 4s linear infinite;
}

@keyframes gradientMove {
   0% {
      background-position: 0% 50%;
   }

   100% {
      background-position: 100% 50%;
   }
}

/* Language badge */
.lang-badge {
   background: linear-gradient(135deg, #4facfe, #00f2fe);
   color: #fff;
   font-size: 0.7rem;
   padding: 0.35rem 0.6rem;
   border-radius: 0.75rem;
   transition: transform 0.25s ease;
}

.lang-badge:hover {
   transform: scale(1.1);
}

/* Tags */
.tag {
   display: inline-block;
   background: rgba(255, 255, 255, 0.12);
   border: 1px solid rgba(255, 255, 255, 0.25);
   color: #ddd;
   font-size: 0.7rem;
   padding: 0.25rem 0.6rem;
   border-radius: 0.5rem;
   margin: 0 0.3rem 0.3rem 0;
   transition: background 0.25s ease, transform 0.25s ease;
}

.tag:hover {
   background: rgba(255, 255, 255, 0.25);
   transform: translateY(-2px);
}

/* Ghost button */
.btn-ghost {
   color: #00c6ff;
   font-size: 0.8rem;
   text-decoration: none;
   padding: 0.4rem 0.8rem;
   border-radius: 0.3rem;
   background: rgba(0, 198, 255, 0.2);
   transition: background 0.3s ease, color 0.3s ease, transform 0.25s ease;
}

.btn-ghost:hover {
   background: rgba(12, 202, 255, 0.5);
   color: #fff;
   transform: translateY(-2px);
}

/* Solid button */
.btn-solid {
   background: linear-gradient(135deg, #00c6ff, #007bff);
   color: #fff;
   border: none;
   font-size: 0.8rem;
   padding: 0.4rem 0.9rem;
   border-radius: 0.6rem;
   transition: opacity 0.3s ease, transform 0.25s ease, box-shadow 0.25s ease;
}

.btn-solid:hover {
   opacity: 0.95;
   transform: translateY(-3px) scale(1.03);
   box-shadow: 0 8px 18px rgba(0, 123, 255, 0.4);
}

.btn-solid.saved {
   background: var(--accent-blue-light);
}

/* Footer spacing */
.snippet-explore-card .card-footer,
.snippet-explore-card .mt-auto {
   gap: 0.5rem;
   /* consistent spacing between buttons */
}

/* Ghost and solid buttons unified sizing */
.btn-ghost,
.btn-solid {
   min-width: 90px;
   text-align: center;
   padding: 0.45rem 0.9rem;
   font-size: 0.8rem;
   line-height: 1.2;
}

/* Ensure no overlap on small cards */
.btn-ghost {
   margin-right: 0.4rem;
}