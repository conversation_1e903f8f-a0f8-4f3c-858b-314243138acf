document.addEventListener('DOMContentLoaded', () => {
    const passwordInput = document.getElementById('password');
    const confirmInput = document.getElementById('confirm_password');

    // Password toggle
    document.querySelectorAll('.password-toggle').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    if (passwordInput) passwordInput.addEventListener('input', validatePassword);
    if (confirmInput) confirmInput.addEventListener('input', validateConfirmPassword);

    function validatePassword() {
        const pwd = passwordInput.value;

        checkRule('length-check', pwd.length >= 8);
        checkRule('uppercase-check', /[A-Z]/.test(pwd));
        checkRule('lowercase-check', /[a-z]/.test(pwd));
        checkRule('number-check', /[0-9]/.test(pwd));
        checkRule('special-check', /[^A-Za-z0-9]/.test(pwd));
    }

    function checkRule(id, isValid) {
        const el = document.getElementById(id);
        if (!el) return;

        el.classList.remove('valid', 'invalid');

        const icon = el.querySelector('i');
        if (isValid) {
            el.classList.add('valid');
            if (icon) {
                icon.className = 'fas fa-check-circle';
            }
        } else {
            el.classList.add('invalid');
            if (icon) {
                icon.className = 'fas fa-times-circle';
            }
        }
    }

    function validateConfirmPassword() {
        const confirmEl = document.getElementById('confirm-validation');
        if (!confirmInput.value) {
            confirmEl.textContent = '';
            return;
        }
        if (passwordInput.value === confirmInput.value) {
            confirmEl.textContent = 'Passwords match';
            confirmEl.className = 'text-success';
        } else {
            confirmEl.textContent = 'Passwords do not match';
            confirmEl.className = 'text-danger';
        }
    }
});
