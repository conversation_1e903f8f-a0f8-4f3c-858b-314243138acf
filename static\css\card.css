.card {
   color: #f5f5f5;
   background-color: var(--card-bg);
   border-radius: var(--border-radius);
   box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
   transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease !important;
}

.card:hover {
   background-color: var(--card-hover-bg);
   transform: translateY(-2px) !important;
   box-shadow: var(--card-hover-shadow) !important;
}

