{% extends "base.html" %}

{% block title %}Explore Snippets{% endblock %}

{% block content %}
<div class="container my-5">

    <h2 class="mb-4">Explore Snippets</h2>

    <!-- Search & Sort Form -->
    <form method="GET" class="row g-3 mb-4">
        <div class="col-md-6">
            <input type="text" name="q" class="form-control" placeholder="Search by title, description, or tag..."
                value="{{ search_query }}">
        </div>
        <div class="col-md-4">
            <select name="sort" class="form-select">
                <option value="newest" {% if sort_by=='newest' %}selected{% endif %}>Newest</option>
                <option value="oldest" {% if sort_by=='oldest' %}selected{% endif %}>Oldest</option>
                <option value="title_asc" {% if sort_by=='title_asc' %}selected{% endif %}>Title A → Z</option>
                <option value="title_desc" {% if sort_by=='title_desc' %}selected{% endif %}>Title Z → A</option>
            </select>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100">Go</button>
        </div>
    </form>

    <!-- Snippets Grid -->
    <div class="row">
        {% for snippet in snippets %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="snippet-explore-card h-100 d-flex flex-column position-relative overflow-hidden">

                <!-- Gradient top accent -->
                <div class="accent-bar"></div>

                <!-- Content -->
                <div class="p-4 d-flex flex-column flex-grow-1">
                    <!-- Title + Language -->
                    <div class="d-flex justify-content-between align-items-start m-2">
                        <h5 class="fw-bold text-truncate m-2">{{ snippet.title }}</h5>
                        <span class="badge lang-badge m-2">{{ snippet.language }}</span>
                    </div>

                    <!-- Description -->
                    <p class="text-muted small flex-grow-0 m-2">
                        {{ snippet.description[:150] }}{% if snippet.description|length > 50 %}...{% endif %}
                    </p>

                    <!-- Tags -->
                    <div class="tags flex-wrap">
                        {% for tag in snippet.tags %}
                        <span class="tag m-2">{{ tag.name }}</span>
                        {% endfor %}
                    </div>

                    <div class="flex-wrap ms-2">
                        <small class="text-muted "><i class="bi bi-heart-fill"></i> {{ snippet.save_count }}
                            Likes</small>
                    </div>

                    <div class="flex-wrap ms-2">
                        <small class="text-muted"><i class="bi bi-eye-fill"></i> {{ snippet.view_count_tmp }}
                            Views</small>
                    </div>

                    <!-- Footer -->
                    <div class="mt-auto d-flex justify-content-between align-items-center m-1">
                        <a href="{{ url_for('snippets.detail', snippet_id=snippet.id) }}" class="btn-ghost">
                            <i class="bi bi-eye"></i> View
                        </a>

                        {% if current_user.is_authenticated %}
                        <form method="POST" action="{{ url_for('snippets.save', snippet_id=snippet.id) }}" class="m-0">
                            {% if current_user in snippet.saved_by %}
                            <button type="submit" class="btn-solid saved" disabled>
                                <i class="bi bi-heart-fill"></i> Liked
                            </button>
                            {% else %}
                            <button type="submit" class="btn-solid">
                                <i class="bi bi-heart"></i> Like
                            </button>
                            {% endif %}
                        </form>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.role == 'admin' %}
                        <form method="POST" action="{{ url_for('snippets.feature_snippet', snippet_id=snippet.id) }}">
                            <button type="submit"
                                class="btn btn-sm {{ 'btn-danger' if snippet.featured else 'btn-outline-secondary' }}">
                                {{ 'Unfeature' if snippet.featured else 'Mark as Featured' }}
                            </button>
                        </form>
                        {% endif %}
                    </div>

                </div>
            </div>
        </div>



        {% else %}

        <p class="text-muted">No snippets found.</p>
        {% endfor %}
        {% if pagination.pages > 1 %}
        <nav aria-label="Snippet pagination">
            <ul class="pagination justify-content-center mt-4">
                {% if pagination.has_prev %}
                <li class="page-item">
                    <a class="page-link"
                        href="{{ url_for('snippets.explore', page=pagination.prev_num, q=search_query, sort=sort_by) }}">&laquo;
                        Prev</a>
                </li>
                {% else %}
                <li class="page-item disabled"><span class="page-link">&laquo; Prev</span></li>
                {% endif %}

                {% for p in range(1, pagination.pages + 1) %}
                <li class="page-item {% if p == pagination.page %}active{% endif %}">
                    <a class="page-link"
                        href="{{ url_for('snippets.explore', page=p, q=search_query, sort=sort_by) }}">{{ p }}</a>
                </li>
                {% endfor %}

                {% if pagination.has_next %}
                <li class="page-item">
                    <a class="page-link"
                        href="{{ url_for('snippets.explore', page=pagination.next_num, q=search_query, sort=sort_by) }}">Next
                        &raquo;</a>
                </li>
                {% else %}
                <li class="page-item disabled"><span class="page-link">Next &raquo;</span></li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}