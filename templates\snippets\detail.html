{% extends "base.html" %}

{% block title %}{{ snippet.title }} - Flask Auth App{% endblock %}

{% block extra_css %}
{{ super() }}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link href="{{ url_for('static', filename='css/snippet.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="snippet-card shadow-sm p-4 mb-5">

  <!-- Header with Back/Edit and language -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <div class="d-flex gap-2">
      <a href="{{ url_for('snippets.explore') }}" class="btn btn-outline-light btn-sm">
        <i class="fas fa-arrow-left me-1"></i> Back
      </a>
      {% if current_user.is_authenticated and current_user.id == snippet.author.id %}
        <a href="{{ url_for('snippets.edit', snippet_id=snippet.id) }}" class="btn btn-outline-warning btn-sm">
          <i class="fas fa-edit me-1"></i> Edit
        </a>
      {% endif %}
    </div>
    <span class="lang-badge">{{ snippet.language }}</span>
  </div>

  <!-- Title -->
  <h2 class="fw-bold mb-3">{{ snippet.title }}</h2>

  <!-- Description -->
  {% if snippet.description %}
    <p class="text-muted mb-4">{{ snippet.description|safe }}</p>
  {% endif %}

  <!-- Code block with copy button -->
  <div class="code-block position-relative mb-4">
    <button class="copy-btn" data-code="{{ snippet.code|safe }}">
      <i class="fas fa-copy"></i>
    </button>
    <pre><code class="{{ snippet.language|lower }}">{{ snippet.code }}</code></pre>
  </div>

  <!-- Tags -->
  <div class="tags">
    {% for tag in snippet.tags %}
      <span class="tag">{{ tag.name }}</span>
    {% endfor %}
  </div>
</div>
{% endblock %}

{% block tb_title %}{{ snippet.title }}{% endblock %}
{% block tb_subtitle %}By {{ snippet.author.username }}{% endblock %}

{% block extra_js %}
<!-- Highlight.js -->
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
<script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
<script>hljs.highlightAll();</script>

<script src="{{ url_for('static', filename='js/snippet.js') }}"></script>
{% endblock %}
