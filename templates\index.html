<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Devsphere.run - Your Dev Universe</title>

<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
:root {
    --bg-base: #1d2023;
    --bg-surface: #1a1a1a;
    --accent-blue: #1463F3;
    --accent-blue-light: #33cfff;
    --text-primary: #f5f5f5;
    --text-muted: #5a5d63;
    --border-radius: 10px;
    --card-bg: #26282e;
    --card-hover-bg: #33363e;
}

html {
    scroll-behavior: smooth; /* modern browsers */
}

/* Global Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--bg-base);
    color: var(--text-primary);
    margin: 0;
    scroll-behavior: smooth;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Plus Jakarta Sans', sans-serif;
    font-weight: 600;
}

.gradient-text {
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-blue-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}
::-webkit-scrollbar-track {
    background: #1d1f24;
}
::-webkit-scrollbar-thumb {
    background: var(--accent-blue);
    border-radius: 10px;
}
::-webkit-scrollbar-thumb:hover {
    background: var(--accent-blue-light);
}

/* Sections */
.section { padding: 5rem 2rem; position: relative; z-index: 1; }
.section.bg-dark { background-color: #26282e; }

/* Hero Section */
.hero-section { min-height: 80vh; display: flex; align-items: center; }
.hero-section img { max-width: 100%; border-radius: var(--border-radius); box-shadow: 0 10px 40px rgba(0,174,239,0.2); }

/* Cards */
.card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    transition: transform 0.5s ease, box-shadow 0.5s ease, background-color 0.3s ease;
    opacity: 0; transform: translateY(20px);
}
.card.visible { opacity: 1; transform: translateY(0); }
.card:hover {
    transform: translateY(-8px);
    background-color: var(--card-hover-bg);
    box-shadow: 0 12px 30px rgba(0,174,239,0.15);
}

.text-muted {
    color: var(--text-muted) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--accent-blue);
    border: none;
    color: #fff;
    font-weight: 600;
    border-radius: var(--border-radius);
    padding: 0.65rem 1.25rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,174,239,0.15);
}
.btn-primary:hover { background-color: var(--accent-blue-light); transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,174,239,0.25); }
.btn-outline-light { border: 1px solid #fff; color: #fff; transition: all 0.3s ease; }
.btn-outline-light:hover { background-color: #fff; color: var(--bg-base); }

/* Stats Cards */
.stats-card { text-align:center; padding:2rem; border-radius: var(--border-radius); transition: transform 0.5s ease; opacity:0; transform:translateY(20px);}
.stats-card.visible { opacity:1; transform: translateY(0);}
.stats-card h3 { font-size:2.5rem; font-weight:700; color: var(--accent-blue);}
.stats-card p { color: var(--text-muted) !important; }

/* Testimonials */
.testimonial-card { background-color: var(--card-bg); border-radius: var(--border-radius); padding: 2rem; margin-bottom: 2rem; box-shadow: 0 4px 12px rgba(0,0,0,0.15); opacity:0; transform:translateY(20px); transition: all 0.5s ease;}
.testimonial-card.visible { opacity:1; transform: translateY(0); }
.testimonial-card p { font-style: italic; color: var(--text-muted) !important; }

/* FAQ Cards */
.faq-card { background-color: var(--card-bg); border-radius: var(--border-radius); padding: 1.5rem 2rem; margin-bottom: 1rem; transition: all 0.3s ease; opacity:0; transform:translateY(20px);}
.faq-card.visible { opacity:1; transform:translateY(0);}
.faq-card:hover { transform: translateY(-3px); background-color: var(--card-hover-bg); }

/* Parallax Background */
.parallax { background-attachment: fixed; background-size: cover; background-position: center; }
</style>
</head>
<body>

<!-- Hero Section -->
<section class="hero-section section parallax" style="background-image: url('/static/images/hero-bg.jpg');">
<div class="container">
<div class="row align-items-center">
<div class="col-lg-6">
<h1 class="display-4 gradient-text fw-bold">Your Dev Universe.</h1>
<p class="text-muted mt-3">Explore, share, and save code snippets while connecting with developers worldwide.</p>
<div class="mt-4">
<a href="/" class="btn btn-primary btn-lg me-3"><i class="bi bi-box-arrow-in-right me-2"></i> Get Started</a>
<a href="#features" class="btn btn-outline-light btn-lg"><i class="bi bi-info-circle me-2"></i> Learn More</a>
</div>
</div>
<div class="col-lg-6 text-center mt-4 mt-lg-0">
<img src="/static/images/hero-code.png" alt="Dev Community" class="img-fluid rounded shadow-lg">
</div>
</div>
</div>
</section>

<!-- Features Section -->
<section id="features" class="section text-center">
<div class="container">
<h2 class="mb-5 gradient-text fw-bold">Platform Features</h2>
<div class="row g-4">
<div class="col-md-3"><div class="card dashboard-card"><i class="bi bi-collection-fill" style="font-size:3rem;"></i><h5 class="mt-3 mb-2">Explore Snippets</h5><p class="text-muted">Discover code snippets from developers globally.</p></div></div>
<div class="col-md-3"><div class="card dashboard-card"><i class="bi bi-upload" style="font-size:3rem;"></i><h5 class="mt-3 mb-2">Share Your Code</h5><p class="text-muted">Upload and organize your own snippets easily.</p></div></div>
<div class="col-md-3"><div class="card dashboard-card"><i class="bi bi-people-fill" style="font-size:3rem;"></i><h5 class="mt-3 mb-2">Connect With Devs</h5><p class="text-muted">Engage with like-minded developers and grow together.</p></div></div>
<div class="col-md-3"><div class="card dashboard-card"><i class="bi bi-lightning-fill" style="font-size:3rem;"></i><h5 class="mt-3 mb-2">Real-time Collaboration</h5><p class="text-muted">Collaborate with other devs in real-time on shared projects.</p></div></div>
</div>
</div>
</section>

<!-- Community & Stats Section -->
<section class="section text-center bg-dark text-white">
<div class="container">
<h2 class="mb-5 gradient-text fw-bold">Community & Stats</h2>
<div class="row g-4">
<div class="col-md-3"><div class="stats-card"><h3>2,542</h3><p>Active Users</p></div></div>
<div class="col-md-3"><div class="stats-card"><h3>1,154</h3><p>Snippets Shared</p></div></div>
<div class="col-md-3"><div class="stats-card"><h3>78%</h3><p>Tasks Completed</p></div></div>
<div class="col-md-3"><div class="stats-card"><h3>24/7</h3><p>Support</p></div></div>
</div>
</div>
</section>

<!-- Testimonials Section -->
<section class="section text-center">
<div class="container">
<h2 class="mb-5 gradient-text fw-bold">What Our Devs Say</h2>
<div class="row g-4">
<div class="col-md-4"><div class="testimonial-card"><p>"Devsphere.run has changed how I organize my snippets."</p><strong>- Alice D.</strong></div></div>
<div class="col-md-4"><div class="testimonial-card"><p>"Sharing and discovering code has never been easier."</p><strong>- Bob K.</strong></div></div>
<div class="col-md-4"><div class="testimonial-card"><p>"The community is amazing and supportive."</p><strong>- Charlie P.</strong></div></div>
</div>
</div>
</section>

<!-- Additional Features Section -->
<section class="section text-center bg-dark text-white">
<div class="container">
<h2 class="mb-5 gradient-text fw-bold">Advanced Features</h2>
<div class="row g-4">
<div class="col-md-4"><div class="card dashboard-card"><i class="bi bi-git" style="font-size:3rem;"></i><h5 class="mt-3 mb-2">Version Control</h5><p class="text-muted">Keep track of snippet versions easily.</p></div></div>
<div class="col-md-4"><div class="card dashboard-card"><i class="bi bi-shield-lock" style="font-size:3rem;"></i><h5 class="mt-3 mb-2">Secure Storage</h5><p class="text-muted">Your code snippets are stored safely.</p></div></div>
<div class="col-md-4"><div class="card dashboard-card"><i class="bi bi-phone" style="font-size:3rem;"></i><h5 class="mt-3 mb-2">Mobile Access</h5><p class="text-muted">Access your snippets anytime from mobile devices.</p></div></div>
</div>
</div>
</section>

<!-- CTA Section -->
<section class="section cta-section text-center text-white parallax" style="background-image: url('/static/images/cta-bg.jpg');">
<div class="container">
<h2 class="mb-3">Ready to level up your coding journey?</h2>
<p class="text-muted mb-4">Join Devsphere.run today and access a universe of code, collaboration, and learning.</p>
<a href="/register" class="btn btn-primary btn-lg"><i class="bi bi-box-arrow-in-right me-2"></i> Join Now</a>
</div>
</section>

<!-- FAQ Section -->
<section class="section text-center">
<div class="container">
<h2 class="mb-5 gradient-text fw-bold">Frequently Asked Questions</h2>
<div class="row g-3">
<div class="col-md-6"><div class="faq-card"><h5>How do I upload a snippet?</h5><p>Click 'Upload', add your code and description, then save.</p></div></div>
<div class="col-md-6"><div class="faq-card"><h5>Is there a free plan?</h5><p>Yes! You can start for free immediately.</p></div></div>
<div class="col-md-6"><div class="faq-card"><h5>Can I collaborate with others?</h5><p>Yes, connect with other developers and share feedback.</p></div></div>
<div class="col-md-6"><div class="faq-card"><h5>Is my code private?</h5><p>You can keep snippets private or share publicly.</p></div></div>
</div>
</div>
</section>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>

<script>
// Cross-browser smooth scroll
function smoothScrollTo(target, duration = 800) {
    const start = window.scrollY || window.pageYOffset;
    const end = target.offsetTop;
    const distance = end - start;
    let startTime = null;

    function easeInOutQuad(t) {
        return t < 0.5 ? 2*t*t : -1+(4-2*t)*t;
    }

    function animation(currentTime) {
        if (!startTime) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        window.scrollTo(0, start + distance * easeInOutQuad(progress));
        if (timeElapsed < duration) {
            requestAnimationFrame(animation);
        }
    }

    requestAnimationFrame(animation);
}

// Attach to all anchor links with hashes
document.querySelectorAll('a[href^="#"]').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) smoothScrollTo(target, 800); // 800ms scroll
    });
});


// Reveal elements on scroll
function revealOnScroll() {
    const cards = document.querySelectorAll('.card, .stats-card, .testimonial-card, .faq-card');
    const windowHeight = window.innerHeight;
    cards.forEach(el => {
        const elementTop = el.getBoundingClientRect().top;
        if (elementTop < windowHeight - 100) el.classList.add('visible');
    });
}
window.addEventListener('scroll', revealOnScroll);
window.addEventListener('load', revealOnScroll);
</script>

</body>
</html>
